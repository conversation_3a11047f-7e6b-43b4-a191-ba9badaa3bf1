"""
Export service for generating files and data for web interfaces.

This service handles exporting analysis results, price data, and technical indicators
to various formats including JSON, JavaScript, and Excel files for web consumption.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.models.stock_models import StockAnalysis, PricePoint, TechnicalIndicator
from shared.exceptions.stock_exceptions import ExportException
from shared.utils.file_utils import ensure_directory_exists


logger = logging.getLogger(__name__)


class ExportService:
    """Service for exporting analysis data to various formats."""

    def __init__(self, web_dir: str = "web", web2_dir: str = "web2", web_test_dir: str = "web_test"):
        """
        Initialize the export service.

        Args:
            web_dir: Directory for legacy web files
            web2_dir: Directory for Next.js web files
            web_test_dir: Directory for test web files
        """
        self._web_dir = web_dir
        self._web2_dir = web2_dir
        self._web_test_dir = web_test_dir
        self._logger = logger

    def export_analysis_to_web(self, analysis: StockAnalysis, symbol: str) -> bool:
        """
        Export stock analysis to web-compatible JSON format.

        Args:
            analysis: Stock analysis results
            symbol: Stock symbol

        Returns:
            True if export successful, False otherwise
        """
        try:
            self._logger.info(f"Exporting analysis for {symbol} to web formats")

            # Convert analysis to web-compatible format
            web_data = self._convert_analysis_to_web_format(analysis, symbol)

            # Export to multiple web directories
            success = True

            # Export to web2/public/analysis
            web2_path = os.path.join(self._web2_dir, "public", "analysis", f"{symbol}.json")
            success &= self._export_json_file(web2_path, web_data)

            # Export to web_test/analysis
            web_test_path = os.path.join(self._web_test_dir, "analysis", f"{symbol}.json")
            success &= self._export_json_file(web_test_path, web_data)

            self._logger.info(f"Analysis export for {symbol}: {'Success' if success else 'Failed'}")
            return success

        except Exception as e:
            self._logger.error(f"Failed to export analysis for {symbol}: {str(e)}")
            return False

    def export_price_history(self, symbol: str, prices: List[PricePoint], days: int = 30) -> bool:
        """
        Export price history to web-compatible format.

        Args:
            symbol: Stock symbol
            prices: List of price points
            days: Number of days to include

        Returns:
            True if export successful, False otherwise
        """
        try:
            self._logger.info(f"Exporting price history for {symbol}")

            # Sort prices by timestamp (ascending order)
            sorted_prices = sorted(prices, key=lambda x: x.timestamp)

            # Take last N days
            recent_prices = sorted_prices[-days:] if len(sorted_prices) > days else sorted_prices

            # Convert to web format
            history_data = {
                "t": [int(p.timestamp) for p in recent_prices],
                "o": [p.open_price for p in recent_prices],
                "h": [p.high_price for p in recent_prices],
                "l": [p.low_price for p in recent_prices],
                "c": [p.close_price for p in recent_prices],
                "v": [p.volume for p in recent_prices]
            }

            # Export to multiple directories
            success = True

            # Export to web2/public/analysis
            web2_path = os.path.join(self._web2_dir, "public", "analysis", f"{symbol}_prices.json")
            success &= self._export_json_file(web2_path, history_data)

            # Export to web_test/analysis
            web_test_path = os.path.join(self._web_test_dir, "analysis", f"{symbol}_prices.json")
            success &= self._export_json_file(web_test_path, history_data)

            return success

        except Exception as e:
            self._logger.error(f"Failed to export price history for {symbol}: {str(e)}")
            return False

    def export_technical_indicator(
        self,
        symbol: str,
        indicator_name: str,
        indicator_data: Dict[str, Any],
        prices: List[PricePoint]
    ) -> bool:
        """
        Export technical indicator data to web format.

        Args:
            symbol: Stock symbol
            indicator_name: Name of the indicator
            indicator_data: Indicator calculation results
            prices: Price data for timestamps

        Returns:
            True if export successful, False otherwise
        """
        try:
            self._logger.info(f"Exporting {indicator_name} indicator for {symbol}")

            # Convert indicator data to web format
            web_indicator_data = self._convert_indicator_to_web_format(
                indicator_name, indicator_data, prices
            )

            # Export to multiple directories
            success = True

            # Export to web2/public/analysis
            web2_path = os.path.join(
                self._web2_dir, "public", "analysis", f"{symbol}_{indicator_name}.json"
            )
            success &= self._export_json_file(web2_path, web_indicator_data)

            # Export to web_test/analysis
            web_test_path = os.path.join(
                self._web_test_dir, "analysis", f"{symbol}_{indicator_name}.json"
            )
            success &= self._export_json_file(web_test_path, web_indicator_data)

            return success

        except Exception as e:
            self._logger.error(f"Failed to export {indicator_name} for {symbol}: {str(e)}")
            return False

    def export_batch_analysis_to_js(
        self,
        analysis_results: List[Dict[str, Any]],
        trading_date: Optional[datetime] = None
    ) -> bool:
        """
        Export batch analysis results to JavaScript file for web consumption.

        Args:
            analysis_results: List of analysis results
            trading_date: Trading date for the analysis

        Returns:
            True if export successful, False otherwise
        """
        try:
            if trading_date is None:
                trading_date = datetime.now()

            trading_date_str = trading_date.strftime("%Y-%m-%d")
            today = datetime.now().strftime("%Y-%m-%d")

            # Create JavaScript file content
            js_content = f"// Generated on {today} for trading date {trading_date_str}\n"
            js_content += "const PREDICTION_DATA="
            js_content += json.dumps(analysis_results, ensure_ascii=False, separators=(",", ":"))
            js_content += ";\n"
            js_content += f"const PREDICTION_DATE='{today}';\n"

            # Export to web directory
            js_file_path = os.path.join(self._web_dir, f"prediction-{trading_date_str}.js")

            ensure_directory_exists(os.path.dirname(js_file_path))

            with open(js_file_path, "w", encoding="utf-8") as f:
                f.write(js_content)

            self._logger.info(f"Batch analysis exported to {js_file_path}")
            return True

        except Exception as e:
            self._logger.error(f"Failed to export batch analysis to JS: {str(e)}")
            return False

    def _convert_analysis_to_web_format(self, analysis: StockAnalysis, symbol: str) -> Dict[str, Any]:
        """Convert StockAnalysis to web-compatible format."""
        def map_zone(zone):
            return {
                "p": zone.price,
                "c": zone.confidence.value.lower(),
                "r": zone.reason
            }

        def map_risk_reward(rr):
            return {
                "bp": rr.buy_price,
                "sl": rr.stop_loss_price,
                "tp": rr.take_profit_price,
                "r": rr.ratio,
                "q": rr.quality.lower()
            }

        def map_technical_indicator(ti):
            return {
                "n": ti.name,
                "v": ti.value,
                "s": ti.signal.value.lower(),
                "c": ti.confidence.value.lower(),
                "d": ti.description
            }

        return {
            "s": symbol,
            "cp": analysis.current_price,
            "pc": analysis.price_change,
            "pcp": analysis.price_change_percent,
            "td": analysis.trend_analysis.direction.value.lower(),
            "ts": analysis.trend_analysis.strength,
            "tc": analysis.trend_analysis.confidence.value.lower(),
            "r": analysis.recommendation.value.lower(),
            "cs": analysis.confidence_score,
            "mc": analysis.market_condition.value.lower(),
            "bz": [map_zone(z) for z in analysis.buy_zones],
            "slz": [map_zone(z) for z in analysis.stop_loss_zones],
            "tpz": [map_zone(z) for z in analysis.take_profit_zones],
            "rr": [map_risk_reward(rr) for rr in analysis.risk_reward_ratios],
            "ti": [map_technical_indicator(ti) for ti in analysis.technical_indicators],
            "ts_summary": analysis.technical_summary,
            "ad": analysis.analysis_date.strftime("%Y-%m-%d") if analysis.analysis_date else None
        }

    def _convert_indicator_to_web_format(
        self,
        indicator_name: str,
        indicator_data: Dict[str, Any],
        prices: List[PricePoint]
    ) -> Dict[str, Any]:
        """Convert indicator data to web-compatible format."""
        # Get timestamps from prices
        timestamps = [int(p.timestamp) for p in prices]

        web_data = {
            "name": indicator_name,
            "timestamps": timestamps
        }

        # Handle different indicator types
        if indicator_name.lower() == "macd":
            web_data.update({
                "macd_line": indicator_data.get("macd_line", []),
                "signal_line": indicator_data.get("signal_line", []),
                "histogram": indicator_data.get("histogram", [])
            })
        elif indicator_name.lower().startswith("bb") or "bollinger" in indicator_name.lower():
            web_data.update({
                "upper_band": indicator_data.get("bb_upper", []),
                "middle_band": indicator_data.get("bb_middle", []),
                "lower_band": indicator_data.get("bb_lower", []),
                "band_width": indicator_data.get("bb_width", [])
            })
        elif indicator_name.lower() == "ichimoku":
            web_data.update({
                "tenkan_sen": indicator_data.get("tenkan_sen", []),
                "kijun_sen": indicator_data.get("kijun_sen", []),
                "senkou_span_a": indicator_data.get("senkou_span_a", []),
                "senkou_span_b": indicator_data.get("senkou_span_b", []),
                "chikou_span": indicator_data.get("chikou_span", [])
            })
        else:
            # For simple indicators (RSI, moving averages, etc.)
            if isinstance(indicator_data, list):
                web_data["values"] = indicator_data
            elif isinstance(indicator_data, dict) and "values" in indicator_data:
                web_data["values"] = indicator_data["values"]
            else:
                # Try to find the main data array
                for key, value in indicator_data.items():
                    if isinstance(value, list) and len(value) > 0:
                        web_data["values"] = value
                        break

        return web_data

    def _export_json_file(self, file_path: str, data: Dict[str, Any]) -> bool:
        """Export data to JSON file."""
        try:
            ensure_directory_exists(os.path.dirname(file_path))

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            self._logger.error(f"Failed to export JSON file {file_path}: {str(e)}")
            return False
