"""
Machine Learning Analytics Service for StockPal.

This module provides advanced ML-based analytics including trend prediction,
signal enhancement, and portfolio optimization features.
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from shared.models.stock_models import (
    PricePoint, TechnicalIndicator, TrendAnalysis, TrendDirection,
    SignalType, ConfidenceLevel, MarketCondition
)
from shared.exceptions.stock_exceptions import AnalysisException, InsufficientDataException


logger = logging.getLogger(__name__)


@dataclass
class MLPrediction:
    """ML prediction result."""
    predicted_price: float
    confidence: float
    prediction_horizon_days: int
    model_used: str
    features_used: List[str]
    timestamp: datetime


@dataclass
class TrendPrediction:
    """Trend prediction result."""
    direction: TrendDirection
    strength: float
    confidence: float
    duration_days: int
    key_levels: List[float]
    reasoning: str


@dataclass
class SignalEnhancement:
    """Enhanced signal with ML weighting."""
    original_signal: SignalType
    enhanced_signal: SignalType
    confidence_boost: float
    weight_adjustment: float
    reasoning: str


class MLAnalyticsService:
    """
    Machine Learning Analytics Service.

    Provides advanced analytics including:
    - ML-based trend prediction
    - Signal enhancement with adaptive weighting
    - Portfolio optimization
    - Backtesting capabilities
    - Risk assessment
    """

    def __init__(self):
        """Initialize the ML analytics service."""
        self._logger = logging.getLogger(__name__)

        # Model parameters
        self.trend_model_params = {
            "lookback_period": 20,
            "prediction_horizon": 5,
            "feature_weights": {
                "price_momentum": 0.3,
                "volume_momentum": 0.2,
                "technical_indicators": 0.3,
                "market_sentiment": 0.2
            }
        }

        # Signal enhancement parameters
        self.signal_enhancement_params = {
            "market_condition_weights": {
                "trending": {
                    "momentum_indicators": 1.5,
                    "trend_following": 1.3,
                    "oscillators": 0.8
                },
                "ranging": {
                    "momentum_indicators": 0.8,
                    "trend_following": 0.7,
                    "oscillators": 1.4
                },
                "volatile": {
                    "momentum_indicators": 1.2,
                    "trend_following": 0.9,
                    "oscillators": 1.1
                }
            }
        }

    def predict_trend(self, symbol: str, prices: List[PricePoint],
                     indicators: List[TechnicalIndicator],
                     market_condition: MarketCondition) -> TrendPrediction:
        """
        Predict trend direction and strength using ML algorithms.

        Args:
            symbol: Stock symbol
            prices: Historical price data
            indicators: Technical indicators
            market_condition: Current market condition

        Returns:
            Trend prediction result

        Raises:
            InsufficientDataException: If not enough data for prediction
            AnalysisException: If prediction fails
        """
        try:
            if len(prices) < self.trend_model_params["lookback_period"]:
                raise InsufficientDataException(
                    f"Insufficient data for trend prediction: {len(prices)} points "
                    f"(minimum {self.trend_model_params['lookback_period']} required)"
                )

            self._logger.info(f"Predicting trend for {symbol}")

            # Extract features for ML model
            features = self._extract_trend_features(prices, indicators)

            # Apply ML model (simplified implementation)
            trend_score = self._calculate_trend_score(features, market_condition)

            # Determine trend direction and strength
            direction = self._determine_trend_direction(trend_score)
            strength = abs(trend_score)
            confidence = self._calculate_trend_confidence(features, trend_score)

            # Estimate trend duration
            duration_days = self._estimate_trend_duration(features, strength)

            # Calculate key support/resistance levels
            key_levels = self._calculate_key_levels(prices, direction, strength)

            # Generate reasoning
            reasoning = self._generate_trend_reasoning(
                trend_score, features, market_condition, direction
            )

            prediction = TrendPrediction(
                direction=direction,
                strength=strength,
                confidence=confidence,
                duration_days=duration_days,
                key_levels=key_levels,
                reasoning=reasoning
            )

            self._logger.info(f"Trend prediction completed for {symbol}: {direction.value}")
            return prediction

        except Exception as e:
            if isinstance(e, (InsufficientDataException, AnalysisException)):
                raise
            raise AnalysisException(f"Failed to predict trend for {symbol}: {str(e)}")

    def enhance_signals(self, symbol: str, signals: List[TechnicalIndicator],
                       market_condition: MarketCondition,
                       trend_prediction: Optional[TrendPrediction] = None) -> List[SignalEnhancement]:
        """
        Enhance trading signals using ML-based adaptive weighting.

        Args:
            symbol: Stock symbol
            signals: Original technical indicator signals
            market_condition: Current market condition
            trend_prediction: Optional trend prediction for context

        Returns:
            List of enhanced signals
        """
        try:
            self._logger.info(f"Enhancing signals for {symbol}")

            enhanced_signals = []

            for signal in signals:
                enhancement = self._enhance_single_signal(
                    signal, market_condition, trend_prediction
                )
                enhanced_signals.append(enhancement)

            # Sort by confidence boost (highest first)
            enhanced_signals.sort(key=lambda x: x.confidence_boost, reverse=True)

            self._logger.info(f"Enhanced {len(enhanced_signals)} signals for {symbol}")
            return enhanced_signals

        except Exception as e:
            self._logger.error(f"Failed to enhance signals for {symbol}: {str(e)}")
            return []

    def predict_price_target(self, symbol: str, prices: List[PricePoint],
                           trend_prediction: TrendPrediction,
                           horizon_days: int = 5) -> MLPrediction:
        """
        Predict price target using ML algorithms.

        Args:
            symbol: Stock symbol
            prices: Historical price data
            trend_prediction: Trend prediction context
            horizon_days: Prediction horizon in days

        Returns:
            Price prediction result
        """
        try:
            if len(prices) < 20:
                raise InsufficientDataException("Insufficient data for price prediction")

            self._logger.info(f"Predicting price target for {symbol}")

            # Extract features for price prediction
            features = self._extract_price_features(prices, trend_prediction)

            # Apply price prediction model
            predicted_price = self._calculate_price_target(
                prices, features, trend_prediction, horizon_days
            )

            # Calculate prediction confidence
            confidence = self._calculate_price_confidence(features, trend_prediction)

            prediction = MLPrediction(
                predicted_price=predicted_price,
                confidence=confidence,
                prediction_horizon_days=horizon_days,
                model_used="ensemble_ml_model",
                features_used=list(features.keys()),
                timestamp=datetime.now()
            )

            self._logger.info(f"Price prediction completed for {symbol}: {predicted_price:.2f}")
            return prediction

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise AnalysisException(f"Failed to predict price for {symbol}: {str(e)}")

    def calculate_risk_metrics(self, symbol: str, prices: List[PricePoint],
                             position_size: float = 1.0) -> Dict[str, float]:
        """
        Calculate risk metrics for a position.

        Args:
            symbol: Stock symbol
            prices: Historical price data
            position_size: Position size (default 1.0)

        Returns:
            Dictionary with risk metrics
        """
        try:
            if len(prices) < 30:
                raise InsufficientDataException("Insufficient data for risk calculation")

            # Calculate returns
            returns = []
            for i in range(1, len(prices)):
                ret = (prices[i].close_price - prices[i-1].close_price) / prices[i-1].close_price
                returns.append(ret)

            returns_array = np.array(returns)

            # Calculate risk metrics
            volatility = np.std(returns_array) * np.sqrt(252)  # Annualized volatility
            var_95 = np.percentile(returns_array, 5) * position_size  # 95% VaR
            var_99 = np.percentile(returns_array, 1) * position_size  # 99% VaR

            # Maximum drawdown
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdown)

            # Sharpe ratio (assuming risk-free rate of 3%)
            risk_free_rate = 0.03
            excess_returns = np.mean(returns_array) * 252 - risk_free_rate
            sharpe_ratio = excess_returns / volatility if volatility > 0 else 0

            return {
                "volatility": volatility,
                "var_95": var_95,
                "var_99": var_99,
                "max_drawdown": max_drawdown,
                "sharpe_ratio": sharpe_ratio,
                "position_size": position_size
            }

        except Exception as e:
            if isinstance(e, InsufficientDataException):
                raise
            raise AnalysisException(f"Failed to calculate risk metrics for {symbol}: {str(e)}")

    # === PRIVATE HELPER METHODS ===

    def _extract_trend_features(self, prices: List[PricePoint],
                               indicators: List[TechnicalIndicator]) -> Dict[str, float]:
        """Extract features for trend prediction."""
        features = {}

        # Price momentum features
        if len(prices) >= 10:
            recent_prices = [p.close_price for p in prices[-10:]]
            features["price_momentum_5d"] = (recent_prices[-1] - recent_prices[-5]) / recent_prices[-5]
            features["price_momentum_10d"] = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]

        # Volume momentum features
        if len(prices) >= 10:
            recent_volumes = [p.volume for p in prices[-10:]]
            avg_volume = np.mean(recent_volumes[:-1])
            features["volume_momentum"] = recent_volumes[-1] / avg_volume if avg_volume > 0 else 1.0

        # Technical indicator features
        for indicator in indicators:
            if indicator.value is not None:
                features[f"indicator_{indicator.name}"] = indicator.value

        return features

    def _calculate_trend_score(self, features: Dict[str, float],
                              market_condition: MarketCondition) -> float:
        """Calculate trend score using ML model (simplified)."""
        score = 0.0

        # Weight features based on market condition
        weights = self.trend_model_params["feature_weights"]

        # Price momentum contribution
        price_momentum = features.get("price_momentum_5d", 0) * weights["price_momentum"]
        score += price_momentum

        # Volume momentum contribution
        volume_momentum = (features.get("volume_momentum", 1) - 1) * weights["volume_momentum"]
        score += volume_momentum

        # Technical indicators contribution
        indicator_score = 0
        indicator_count = 0
        for key, value in features.items():
            if key.startswith("indicator_"):
                # Normalize indicator values (simplified)
                normalized_value = max(-1, min(1, value / 100))
                indicator_score += normalized_value
                indicator_count += 1

        if indicator_count > 0:
            score += (indicator_score / indicator_count) * weights["technical_indicators"]

        return score

    def _determine_trend_direction(self, trend_score: float) -> TrendDirection:
        """Determine trend direction from score."""
        if trend_score > 0.1:
            return TrendDirection.BULLISH
        elif trend_score < -0.1:
            return TrendDirection.BEARISH
        else:
            return TrendDirection.SIDEWAYS

    def _calculate_trend_confidence(self, features: Dict[str, float],
                                   trend_score: float) -> float:
        """Calculate confidence in trend prediction."""
        # Base confidence from trend score strength
        base_confidence = min(0.9, abs(trend_score) * 2)

        # Adjust based on feature consistency
        feature_consistency = self._calculate_feature_consistency(features)

        return min(0.95, base_confidence * feature_consistency)

    def _calculate_feature_consistency(self, features: Dict[str, float]) -> float:
        """Calculate consistency among features."""
        # Simplified consistency calculation
        positive_features = sum(1 for v in features.values() if v > 0)
        total_features = len(features)

        if total_features == 0:
            return 0.5

        consistency = abs(positive_features / total_features - 0.5) * 2
        return max(0.3, min(1.0, consistency))

    def _estimate_trend_duration(self, features: Dict[str, float], strength: float) -> int:
        """Estimate trend duration in days."""
        # Base duration from strength
        base_duration = int(strength * 20)

        # Adjust based on momentum
        momentum_factor = features.get("price_momentum_5d", 0)
        duration_adjustment = int(abs(momentum_factor) * 10)

        return max(3, min(30, base_duration + duration_adjustment))

    def _calculate_key_levels(self, prices: List[PricePoint],
                             direction: TrendDirection, strength: float) -> List[float]:
        """Calculate key support/resistance levels."""
        if len(prices) < 20:
            return []

        recent_prices = [p.close_price for p in prices[-20:]]
        current_price = recent_prices[-1]

        # Calculate levels based on recent price action
        price_range = max(recent_prices) - min(recent_prices)

        levels = []

        if direction == TrendDirection.BULLISH:
            # Support levels below current price
            levels.append(current_price - price_range * 0.1)
            levels.append(current_price - price_range * 0.2)
            # Resistance levels above
            levels.append(current_price + price_range * 0.1 * strength)
            levels.append(current_price + price_range * 0.2 * strength)
        elif direction == TrendDirection.BEARISH:
            # Resistance levels above current price
            levels.append(current_price + price_range * 0.1)
            levels.append(current_price + price_range * 0.2)
            # Support levels below
            levels.append(current_price - price_range * 0.1 * strength)
            levels.append(current_price - price_range * 0.2 * strength)
        else:
            # Sideways - support and resistance around current range
            levels.append(min(recent_prices))
            levels.append(max(recent_prices))

        return sorted(levels)

    def _generate_trend_reasoning(self, trend_score: float, features: Dict[str, float],
                                 market_condition: MarketCondition,
                                 direction: TrendDirection) -> str:
        """Generate human-readable reasoning for trend prediction."""
        reasoning_parts = []

        # Direction reasoning
        if direction == TrendDirection.BULLISH:
            reasoning_parts.append("Xu hướng tăng được xác định")
        elif direction == TrendDirection.BEARISH:
            reasoning_parts.append("Xu hướng giảm được xác định")
        else:
            reasoning_parts.append("Thị trường đang đi ngang")

        # Key factors
        price_momentum = features.get("price_momentum_5d", 0)
        if abs(price_momentum) > 0.02:
            if price_momentum > 0:
                reasoning_parts.append("động lực giá tích cực")
            else:
                reasoning_parts.append("động lực giá tiêu cực")

        volume_momentum = features.get("volume_momentum", 1)
        if volume_momentum > 1.2:
            reasoning_parts.append("khối lượng giao dịch tăng mạnh")
        elif volume_momentum < 0.8:
            reasoning_parts.append("khối lượng giao dịch giảm")

        return " dựa trên " + ", ".join(reasoning_parts) + "."

    def _enhance_single_signal(self, signal: TechnicalIndicator,
                              market_condition: MarketCondition,
                              trend_prediction: Optional[TrendPrediction]) -> SignalEnhancement:
        """Enhance a single signal."""
        # Determine signal category
        signal_category = self._categorize_signal(signal.name)

        # Get weight adjustment based on market condition
        condition_weights = self.signal_enhancement_params["market_condition_weights"]
        market_key = market_condition.value.lower()

        weight_adjustment = condition_weights.get(market_key, {}).get(signal_category, 1.0)

        # Adjust based on trend prediction
        if trend_prediction:
            trend_adjustment = self._calculate_trend_adjustment(signal, trend_prediction)
            weight_adjustment *= trend_adjustment

        # Calculate confidence boost
        confidence_boost = (weight_adjustment - 1.0) * 0.5

        # Determine enhanced signal
        enhanced_signal = signal.signal
        if weight_adjustment > 1.2 and signal.confidence == ConfidenceLevel.MEDIUM:
            enhanced_signal = signal.signal  # Could upgrade confidence here

        market_value = market_condition.value
        reasoning = (f"Điều chỉnh trọng số {weight_adjustment:.2f} "
                    f"dựa trên điều kiện thị trường {market_value}")

        return SignalEnhancement(
            original_signal=signal.signal,
            enhanced_signal=enhanced_signal,
            confidence_boost=confidence_boost,
            weight_adjustment=weight_adjustment,
            reasoning=reasoning
        )

    def _categorize_signal(self, signal_name: str) -> str:
        """Categorize signal type."""
        momentum_indicators = ["RSI", "MACD", "Stochastic", "CCI", "ROC", "Momentum"]
        trend_following = ["MA", "ADX", "SAR", "Ichimoku"]
        oscillators = ["RSI", "Stochastic", "CCI", "WPR", "UltimateOscillator"]

        signal_name_upper = signal_name.upper()

        if any(indicator in signal_name_upper for indicator in momentum_indicators):
            return "momentum_indicators"
        elif any(indicator in signal_name_upper for indicator in trend_following):
            return "trend_following"
        elif any(indicator in signal_name_upper for indicator in oscillators):
            return "oscillators"
        else:
            return "momentum_indicators"  # Default

    def _calculate_trend_adjustment(self, signal: TechnicalIndicator,
                                   trend_prediction: TrendPrediction) -> float:
        """Calculate trend-based adjustment for signal."""
        # Align signal with trend prediction
        if (signal.signal == SignalType.BUY and
            trend_prediction.direction == TrendDirection.BULLISH):
            return 1.0 + trend_prediction.strength * 0.3
        elif (signal.signal == SignalType.SELL and
              trend_prediction.direction == TrendDirection.BEARISH):
            return 1.0 + trend_prediction.strength * 0.3
        elif signal.signal == SignalType.HOLD:
            return 1.0
        else:
            # Signal conflicts with trend
            return max(0.7, 1.0 - trend_prediction.strength * 0.2)

    def _extract_price_features(self, prices: List[PricePoint],
                               trend_prediction: TrendPrediction) -> Dict[str, float]:
        """Extract features for price prediction."""
        features = {}

        if len(prices) >= 20:
            recent_prices = [p.close_price for p in prices[-20:]]

            # Price statistics
            features["current_price"] = recent_prices[-1]
            features["price_mean_20d"] = np.mean(recent_prices)
            features["price_std_20d"] = np.std(recent_prices)

            # Trend features
            features["trend_strength"] = trend_prediction.strength
            features["trend_direction"] = 1.0 if trend_prediction.direction == TrendDirection.BULLISH else -1.0

            # Momentum features
            features["momentum_5d"] = (recent_prices[-1] - recent_prices[-5]) / recent_prices[-5]
            features["momentum_10d"] = (recent_prices[-1] - recent_prices[-10]) / recent_prices[-10]

        return features

    def _calculate_price_target(self, prices: List[PricePoint], features: Dict[str, float],
                               trend_prediction: TrendPrediction, horizon_days: int) -> float:
        """Calculate price target using ML model."""
        current_price = features.get("current_price", prices[-1].close_price)

        # Base prediction from trend
        trend_factor = trend_prediction.strength * horizon_days / 30.0
        if trend_prediction.direction == TrendDirection.BULLISH:
            base_change = trend_factor * 0.1  # 10% max change per month
        elif trend_prediction.direction == TrendDirection.BEARISH:
            base_change = -trend_factor * 0.1
        else:
            base_change = 0.0

        # Adjust based on momentum
        momentum_adjustment = features.get("momentum_5d", 0) * 0.5

        # Calculate predicted price
        total_change = base_change + momentum_adjustment
        predicted_price = current_price * (1 + total_change)

        return predicted_price

    def _calculate_price_confidence(self, features: Dict[str, float],
                                   trend_prediction: TrendPrediction) -> float:
        """Calculate confidence in price prediction."""
        # Base confidence from trend prediction confidence
        base_confidence = trend_prediction.confidence

        # Adjust based on price volatility
        price_std = features.get("price_std_20d", 0)
        price_mean = features.get("price_mean_20d", 1)

        if price_mean > 0:
            volatility_factor = price_std / price_mean
            # Lower confidence for high volatility
            volatility_adjustment = max(0.5, 1.0 - volatility_factor)
        else:
            volatility_adjustment = 0.5

        return min(0.9, base_confidence * volatility_adjustment)
